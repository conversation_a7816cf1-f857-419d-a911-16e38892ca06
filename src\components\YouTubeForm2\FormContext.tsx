'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { OutputLanguage, OutputStyle, OutputSize, LogEntry, processYouTubeVideo } from '@/app/actions';

// Define the context type
interface FormContextType {
  // Form inputs
  videoUrl: string;
  setVideoUrl: (url: string) => void;
  language: OutputLanguage;
  setLanguage: (language: OutputLanguage) => void;
  style: OutputStyle;
  setStyle: (style: OutputStyle) => void;
  size: OutputSize;
  setSize: (size: OutputSize) => void;

  // Results
  post: string | null;
  setPost: (post: string | null) => void;
  transcript: string | null;
  setTranscript: (transcript: string | null) => void;
  transcriptSource: 'youtube-captions' | 'audio-transcription' | null;
  setTranscriptSource: (source: 'youtube-captions' | 'audio-transcription' | null) => void;
  modelUsed: string | null;
  setModelUsed: (model: string | null) => void;

  // Video metadata
  videoTitle: string | null;
  setVideoTitle: (title: string | null) => void;
  videoThumbnail: string | null;
  setVideoThumbnail: (url: string | null) => void;
  videoId: string | null;
  setVideoId: (id: string | null) => void;
  videoDuration: string | null;
  setVideoDuration: (duration: string | null) => void;
  videoPublishDate: string | null;
  setVideoPublishDate: (date: string | null) => void;

  // UI state
  loading: boolean;
  setLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  logs: LogEntry[];
  setLogs: (logs: LogEntry[]) => void;
  showLogs: boolean;
  setShowLogs: (show: boolean) => void;
  layoutMode: 'single' | 'two-column';
  setLayoutMode: (mode: 'single' | 'two-column') => void;

  // Functions
  addLog: (level: string, message: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  getVideoInfo: (url: string) => Promise<void>;
}

// Create the context with a default undefined value
const FormContext = createContext<FormContextType | undefined>(undefined);

// Provider component
export function FormProvider({ children }: { children: ReactNode }) {
  const searchParams = useSearchParams();

  // Form inputs
  const [videoUrl, setVideoUrl] = useState('');
  const [language, setLanguage] = useState<OutputLanguage>('english');
  const [style, setStyle] = useState<OutputStyle>('insightful');
  const [size, setSize] = useState<OutputSize>('auto');
  const [shouldAutoSubmit, setShouldAutoSubmit] = useState(false);

  // Results
  const [post, setPost] = useState<string | null>(null);
  const [transcript, setTranscript] = useState<string | null>(null);
  const [transcriptSource, setTranscriptSource] = useState<'youtube-captions' | 'audio-transcription' | null>(null);
  const [modelUsed, setModelUsed] = useState<string | null>(null);

  // Video metadata
  const [videoTitle, setVideoTitle] = useState<string | null>(null);
  const [videoThumbnail, setVideoThumbnail] = useState<string | null>(null);
  const [videoId, setVideoId] = useState<string | null>(null);
  const [videoDuration, setVideoDuration] = useState<string | null>(null);
  const [videoPublishDate, setVideoPublishDate] = useState<string | null>(null);

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [layoutMode, setLayoutMode] = useState<'single' | 'two-column'>(() => {
    // Try to get from localStorage, default to 'single'
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('nobot-layout-mode');
      return (saved === 'two-column') ? 'two-column' : 'single';
    }
    return 'single';
  });

  // Function to add a log entry
  const addLog = useCallback((level: string, message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
    setLogs(prevLogs => [...prevLogs, { timestamp, level, message }]);
  }, []);

  // Function to extract video ID from YouTube URL
  const extractVideoId = useCallback((url: string): string | null => {
    let videoId: string | null = null;

    // Handle youtube.com URLs
    if (url.includes('youtube.com/watch')) {
      const urlParams = new URL(url).searchParams;
      videoId = urlParams.get('v');
    }
    // Handle youtu.be URLs
    else if (url.includes('youtu.be/')) {
      videoId = url.split('youtu.be/')[1];
      const questionMarkPosition = videoId.indexOf('?');
      if (questionMarkPosition !== -1) {
        videoId = videoId.substring(0, questionMarkPosition);
      }
    }

    return videoId;
  }, []);

  // Function to get video info (title and thumbnail)
  const getVideoInfo = useCallback(async (url: string): Promise<void> => {
    try {
      // Extract video ID first to check if it's different
      const newId = extractVideoId(url);
      if (!newId) {
        addLog('WARN', 'Could not extract video ID from URL');
        return;
      }

      // Only clear summary data if this is a different video
      const currentId = videoId;
      const isDifferentVideo = currentId !== newId;

      if (isDifferentVideo) {
        // Clear summary data only for different videos
        setPost(null);
        setTranscript(null);
        setTranscriptSource(null);
        setModelUsed(null);
        setVideoDuration(null);
        setVideoPublishDate(null);
      }

      // Reset video info
      setVideoTitle('Loading video information...');
      setVideoId(newId);
      // Set thumbnail URL (using high quality thumbnail)
      const thumbnailUrl = `https://img.youtube.com/vi/${newId}/hqdefault.jpg`;
      setVideoThumbnail(thumbnailUrl);

      // Set a placeholder title initially
      setVideoTitle('Loading video information...');

      // Try to fetch the video title and metadata immediately using the YouTube oEmbed API
      try {
        addLog('INFO', 'Fetching video metadata from YouTube oEmbed API');
        const response = await fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${newId}&format=json`);

        if (response.ok) {
          const data = await response.json();
          if (data.title) {
            setVideoTitle(data.title);
            addLog('INFO', `Video title fetched: ${data.title}`);
            console.log('Video title fetched from oEmbed API:', data.title);
          }

          // Try to get additional metadata from YouTube Data API v3 (if available)
          // For now, we'll set placeholder values that will be updated when the video is processed
          setVideoDuration('Loading...');
          setVideoPublishDate('Loading...');
        } else {
          console.log('Failed to fetch video metadata from oEmbed API:', response.status);
          addLog('WARN', `Failed to fetch video metadata from oEmbed API: ${response.status}`);
        }
      } catch (titleError) {
        console.error('Error fetching video metadata:', titleError);
        addLog('WARN', 'Error fetching video metadata from oEmbed API');
      }
    } catch (error) {
      console.error('Error getting video info:', error);
      addLog('WARN', 'Failed to get video information');
    }
  }, [extractVideoId, addLog, setVideoTitle, setVideoId, setPost, setVideoThumbnail, videoId, setTranscript, setTranscriptSource, setModelUsed, setVideoDuration, setVideoPublishDate]);

  // Form submission handler
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset previous state only when starting new processing
    setLogs([]);
    setError(null);
    setPost(null);
    setTranscript(null);
    setTranscriptSource(null);

    // Add initial client-side logs
    addLog('INFO', 'Starting YouTube video processing');
    addLog('INFO', `Input: URL=${videoUrl}, Language=${language}, Summary Style=${style}, Size=${size}, Model=GPT-4o Mini`);

    if (!videoUrl) {
      setError('Please enter a YouTube URL');
      addLog('ERROR', 'No YouTube URL provided');
      return;
    }

    // Basic URL validation
    if (!videoUrl.includes('youtube.com/') && !videoUrl.includes('youtu.be/')) {
      setError('Please enter a valid YouTube URL');
      addLog('ERROR', `Invalid URL format: ${videoUrl}`);
      return;
    }

    // Get video info (thumbnail and placeholder title)
    await getVideoInfo(videoUrl);

    try {
      setLoading(true);
      addLog('INFO', 'Sending request to server...');

      // Set up a timer to add "still processing" logs every 5 seconds
      // but only if we haven't received server logs yet
      let receivedServerLogs = false;
      const processingInterval = setInterval(() => {
        if (!receivedServerLogs) {
          addLog('INFO', 'Still processing, please wait...');
        }
      }, 5000);

      // Call the server action with audio transcription enabled
      const result = await processYouTubeVideo(videoUrl, language, style, size, false);

      // Clear the interval
      clearInterval(processingInterval);
      receivedServerLogs = true;

      // Replace client logs with server logs
      setLogs(result.logs);

      // Set the results
      setPost(result.post);
      setTranscript(result.transcript);
      setTranscriptSource(result.transcriptSource);
      setModelUsed(result.modelUsed);

      // Set video metadata directly from the response if available
      if (result.videoTitle) {
        console.log('Setting video title from response:', result.videoTitle);
        setVideoTitle(result.videoTitle);
      } else {
        // Fallback: Try to extract video title from logs
        console.log('No video title in response, trying to extract from logs');
        const titleLog = result.logs.find((log: LogEntry) =>
          log.message.includes('Video information retrieved:')
        );

        if (titleLog) {
          const titleMatch = titleLog.message.match(/Video information retrieved: (.+)/);
          if (titleMatch && titleMatch[1]) {
            setVideoTitle(titleMatch[1]);
            console.log('Video title extracted from logs:', titleMatch[1]);
          } else {
            console.log('Title match pattern failed:', titleLog.message);
          }
        } else {
          console.log('No log entry with video title found');

          // Fallback: Try to find any log that might contain video information
          const possibleTitleLogs = result.logs.filter((log: LogEntry) =>
            log.message.toLowerCase().includes('video') &&
            log.message.toLowerCase().includes('title')
          );

          if (possibleTitleLogs.length > 0) {
            console.log('Possible title logs found:', possibleTitleLogs);

            // Try to extract from "Video title extracted" log
            const extractedTitleLog = possibleTitleLogs.find((log: LogEntry) =>
              log.message.includes('Video title extracted:')
            );

            if (extractedTitleLog) {
              const extractMatch = extractedTitleLog.message.match(/Video title extracted: (.+)/);
              if (extractMatch && extractMatch[1]) {
                setVideoTitle(extractMatch[1]);
                console.log('Video title extracted from "Video title extracted" log:', extractMatch[1]);
              }
            }
          }
        }
      }

      // Set video duration and publish date from response
      if (result.videoDuration) {
        setVideoDuration(result.videoDuration);
      }
      if (result.videoPublishDate) {
        setVideoPublishDate(result.videoPublishDate);
      }

      // Add model information to logs
      addLog('INFO', `Summary generated using model: ${result.modelUsed}`);
    } catch (err: unknown) {
      console.error('Error in form submission:', err);
      addLog('ERROR', `Processing failed: ${err instanceof Error ? err.message : 'Unknown error'}`);

      // Handle specific error cases
      if (err instanceof Error) {
        if (err.message.includes('403')) {
          setError('OpenAI API access forbidden (403 error): This usually means your OpenAI account does not have billing set up, which is required for using the Whisper API, or your API key is invalid. Please check your OpenAI account settings.');
          addLog('ERROR', 'OpenAI API access forbidden (403 error)');
        } else {
          setError(err.message);
        }
      } else {
        setError('An unexpected error occurred. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  }, [videoUrl, language, style, size, addLog, getVideoInfo]);

  // Handle URL parameters and auto-submission
  useEffect(() => {
    const urlParam = searchParams.get('url');
    if (urlParam) {
      setVideoUrl(urlParam);
      setShouldAutoSubmit(true);
    }
  }, [searchParams]);

  // Auto-submit when URL is set from query params
  useEffect(() => {
    if (shouldAutoSubmit && videoUrl) {
      setShouldAutoSubmit(false);
      // Small delay to ensure the UI is ready
      setTimeout(() => {
        const fakeEvent = { preventDefault: () => {} } as React.FormEvent;
        handleSubmit(fakeEvent);
      }, 500);
    }
  }, [shouldAutoSubmit, videoUrl, handleSubmit]);

  // Persist layout mode to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('nobot-layout-mode', layoutMode);
    }
  }, [layoutMode]);

  // Create the context value object
  const contextValue: FormContextType = {
    videoUrl, setVideoUrl,
    language, setLanguage,
    style, setStyle,
    size, setSize,
    post, setPost,
    transcript, setTranscript,
    transcriptSource, setTranscriptSource,
    modelUsed, setModelUsed,
    videoTitle, setVideoTitle,
    videoThumbnail, setVideoThumbnail,
    videoId, setVideoId,
    videoDuration, setVideoDuration,
    videoPublishDate, setVideoPublishDate,
    loading, setLoading,
    error, setError,
    logs, setLogs,
    showLogs, setShowLogs,
    layoutMode, setLayoutMode,
    addLog,
    handleSubmit,
    getVideoInfo
  };

  return (
    <FormContext.Provider value={contextValue}>
      {children}
    </FormContext.Provider>
  );
}

// Custom hook to use the form context
export function useFormContext() {
  const context = useContext(FormContext);
  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
}
