'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { HelpCircle, RefreshCw, Copy } from 'lucide-react';
import { useFormContext } from './FormContext';
import MarkdownRenderer from '../MarkdownRenderer';

export default function QnAInterface() {
  const { transcript, videoTitle } = useFormContext();
  const [qnaContent, setQnaContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateQnA = async () => {
    if (!transcript || isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/qna', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcript,
          videoTitle
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate Q&A');
      }

      const data = await response.json();
      setQnaContent(data.qna);
    } catch (error) {
      console.error('Error generating Q&A:', error);
      setError('Failed to generate Q&A. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (qnaContent) {
      navigator.clipboard.writeText(qnaContent);
    }
  };

  if (!transcript) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <HelpCircle className="w-16 h-16 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Q&amp;A Generation
        </h3>
        <p className="text-gray-500 dark:text-gray-400 max-w-md">
          Once a video transcript is available, you can generate a comprehensive
          Q&amp;A format that covers the key topics and insights from the video.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Generate Button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Q&amp;A Format
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Generate questions and answers based on the video content
          </p>
        </div>
        <div className="flex gap-2">
          {qnaContent && (
            <Button
              onClick={copyToClipboard}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Copy className="w-4 h-4" />
              Copy
            </Button>
          )}
          <Button
            onClick={generateQnA}
            disabled={isLoading}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <HelpCircle className="w-4 h-4" />
                {qnaContent ? 'Regenerate Q&amp;A' : 'Generate Q&amp;A'}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 min-h-[400px]">
        {error ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
              <HelpCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Error Generating Q&amp;A
            </h3>
            <p className="text-red-600 dark:text-red-400 text-sm mb-4">{error}</p>
            <Button
              onClick={generateQnA}
              variant="outline"
              size="sm"
            >
              Try Again
            </Button>
          </div>
        ) : isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Generating Q&amp;A
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Creating questions and answers from the video content...
            </p>
          </div>
        ) : qnaContent ? (
          <div className="p-6">
            <div 
              className="prose prose-sm dark:prose-invert max-w-none overflow-y-auto max-h-[calc(100vh-400px)]"
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgb(156 163 175) transparent'
              }}
            >
              <MarkdownRenderer content={qnaContent} />
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <HelpCircle className="w-16 h-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Ready to Generate Q&amp;A
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mb-6">
              Click the &ldquo;Generate Q&amp;A&rdquo; button to create a comprehensive question and answer
              format based on the video transcript. This will help you quickly understand
              the key topics and insights.
            </p>
            <Button
              onClick={generateQnA}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white flex items-center gap-2"
            >
              <HelpCircle className="w-4 h-4" />
              Generate Q&amp;A
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
