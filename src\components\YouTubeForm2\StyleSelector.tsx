"use client";

import { ZapIcon } from "lucide-react";
import Select from "../ui/SelectPlain";
import { useFormContext } from "./FormContext";
import { OutputStyle } from "@/app/actions";

export default function StyleSelector() {
  const { style, setStyle } = useFormContext();

  const styleOptions = [
    { value: "insightful", label: "Insightful" },
    { value: "funny", label: "Funny" },
    { value: "actionable", label: "Actionable" },
    { value: "controversial", label: "Controversial" },
    { value: "educational", label: "Educational" },
    { value: "technical", label: "Technical" },
  ];

  return (
    <>
      <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
        <div className="flex items-center gap-1">
          <ZapIcon className="w-3.5 h-3.5 flex-shrink-0" />
        </div>
        <Select
          id="style"
          value={style}
          onChange={(value) => setStyle(value as OutputStyle)}
          options={styleOptions}
          title="Choose the tone and style of your summary"
        />
      </div>
    </>
  );
}
