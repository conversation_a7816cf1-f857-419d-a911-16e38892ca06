"use client";

import React from 'react';
import GoogleAuthButton from "@/components/GoogleAuthButton";

const LoginPage = () => {

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/3 right-0 w-80 h-80 bg-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-blue-600/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-2 h-2 bg-white/20 rounded-full animate-bounce`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>

      <div className="grid lg:grid-cols-2 min-h-screen relative z-10">
        {/* Left Column - Enhanced Branding */}
        <div className="hidden lg:flex flex-col justify-center items-center p-12 relative">
          <div className="relative z-10 text-center">
            {/* Enhanced Robot Animation */}
            <div className="w-40 h-40 mx-auto mb-8 relative group">
              <div className="w-40 h-40 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-full flex items-center justify-center text-7xl animate-bounce shadow-2xl shadow-purple-500/50 group-hover:shadow-purple-500/80 transition-all duration-300">
                🤖
              </div>
              <div className="absolute -top-2 -right-2 w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full animate-ping"></div>
              <div className="absolute -top-2 -right-2 w-10 h-10 bg-green-400 rounded-full animate-pulse"></div>

              {/* Orbit rings */}
              <div className="absolute inset-0 border-2 border-purple-400/30 rounded-full animate-spin" style={{ animationDuration: '8s' }}></div>
              <div className="absolute inset-2 border border-pink-400/20 rounded-full animate-spin" style={{ animationDuration: '6s', animationDirection: 'reverse' }}></div>
            </div>

            <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent animate-pulse">
              Nobot
            </h1>
            <p className="text-2xl text-gray-300 mb-12 max-w-md leading-relaxed">
              Experience the future of AI-powered summaries
            </p>

            {/* Enhanced Features with glowing effects */}
            <div className="space-y-6 text-left">
              <div className="flex items-center space-x-4 text-gray-300 group hover:text-white transition-all duration-300">
                <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full shadow-lg shadow-green-400/50 group-hover:shadow-green-400/80 transition-all duration-300"></div>
                <span className="text-lg">No sign-up required</span>
              </div>
              <div className="flex items-center space-x-4 text-gray-300 group hover:text-white transition-all duration-300">
                <div className="w-4 h-4 bg-gradient-to-r from-purple-400 to-violet-400 rounded-full shadow-lg shadow-purple-400/50 group-hover:shadow-purple-400/80 transition-all duration-300"></div>
                <span className="text-lg">5 free summaries daily</span>
              </div>
              <div className="flex items-center space-x-4 text-gray-300 group hover:text-white transition-all duration-300">
                <div className="w-4 h-4 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full shadow-lg shadow-pink-400/50 group-hover:shadow-pink-400/80 transition-all duration-300"></div>
                <span className="text-lg">Human-like summaries</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Enhanced Login Form */}
        <div className="flex flex-col justify-center items-center p-8 lg:p-12 bg-white border-l border-gray-200">
          {/* Mobile logo */}
          <div className="lg:hidden mb-8 text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-3xl shadow-2xl shadow-purple-500/50 animate-bounce">
              🤖
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Nobot
            </h1>
          </div>

          <div className="w-full max-w-sm">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Welcome Back
              </h2>
              <p className="text-gray-600 text-lg">Sign in to unlock your AI potential</p>
            </div>

            {/* Enhanced Google Login Button */}
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300 animate-pulse"></div>
              <div className="relative">
                <GoogleAuthButton />
              </div>
            </div>

            {/* Enhanced Divider */}
            <div className="flex items-center my-8">
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
              <span className="px-6 text-gray-500 text-sm font-medium">or</span>
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
            </div>

            {/* Enhanced Back to home link */}
            <div className="text-center">
              <a
                href="/"
                className="text-gray-600 hover:text-purple-600 transition-all duration-300 text-sm inline-flex items-center space-x-2 group hover:scale-105"
              >
                <span className="transform group-hover:-translate-x-1 transition-transform duration-300">←</span>
                <span>Back to Home</span>
              </a>
            </div>

            {/* Enhanced Footer links */}
            <div className="text-center mt-10 text-gray-500 text-xs">
              <p className="mb-3">By continuing, you agree to our</p>
              <div className="flex justify-center space-x-6">
                <a href="#" className="hover:text-purple-600 transition-colors duration-300 hover:underline">Terms</a>
                <span>•</span>
                <a href="#" className="hover:text-purple-600 transition-colors duration-300 hover:underline">Privacy</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
