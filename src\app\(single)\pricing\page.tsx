import { Check, Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const Pricing = () => {
  return (
    <TooltipProvider>
      <div className="bg-gray-900 text-white">

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="relative">
          <h1 className="text-6xl md:text-7xl font-bold mb-6 leading-tight">
            Simple, <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">transparent pricing.</span>
          </h1>

          <div className="absolute -top-4 -right-4 md:-right-8">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-bounce">
              <span className="text-2xl">🤖</span>
            </div>
          </div>
        </div>

        <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed">
          Start free, upgrade when you need more. No hidden fees, no surprises.
        </p>
      </section>

      {/* Pricing Cards */}
      <section className="max-w-7xl mx-auto px-6 pb-20">
        <div className="grid md:grid-cols-3 gap-8">

          {/* Free Plan */}
          <div className="bg-gray-800 p-8 rounded-2xl border border-gray-700 hover:border-purple-500 transition-all">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-2">Free</h3>
              <div className="mb-4">
                <span className="text-5xl font-bold">$0</span>
                <span className="text-gray-400 text-lg">/month</span>
              </div>
              <p className="text-gray-300">No sign-up or credit card required</p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Up to 10 video summaries / month</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Smart Summary - Clean bullets + highlights</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Download transcript</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Choose summary style: bullets, quotes, insights, action items etc</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Download as TXT or Markdown</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>One-click Copy to Clipboard</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Basic support</span>
              </li>
            </ul>

            <button className="w-full bg-gray-700 text-white py-3 rounded-lg font-medium hover:bg-gray-600 transition-all">
              Get Started
            </button>
          </div>

          {/* Pro Plan */}
          <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 p-8 rounded-2xl border-2 border-gradient-to-r from-purple-500 to-pink-500 relative">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                Most Popular
              </span>
            </div>

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-2">Starter</h3>
              <div className="mb-4">
                <span className="text-5xl font-bold">$3.99</span>
                <span className="text-gray-400 text-lg">/month</span>
              </div>
              <p className="text-gray-300">For serious content consumers</p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Everything in free</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Up to 500 video summaries</span>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="w-4 h-4 text-gray-400 hover:text-gray-300 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Blended AI engine: GPT-4o + GPT-4o Mini</p>
                  </TooltipContent>
                </Tooltip>
              </li>
              {/* <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Blended AI engine: GPT-4o + GPT-4o Mini
</span>
              </li> */}
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>ELI5 mode</span>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="w-4 h-4 text-gray-400 hover:text-gray-300 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[300px]">
                    <p>Explain Like I'm 5: Summarize complex content in simple terms for easier understanding</p>
                  </TooltipContent>
                </Tooltip>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Summarize in 40+ languages</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Chat with video</span>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="w-4 h-4 text-gray-400 hover:text-gray-300 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[300px]">
                    <p>Ask questions, get clarifications, or explore topics in more detail</p>
                  </TooltipContent>
                </Tooltip>
              </li>

              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Save & organize summaries</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Custom summary length</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Export to PDF or Doc</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Priority support</span>
              </li>

            </ul>

            <button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all transform hover:scale-105">
              Upgrade to starter
            </button>
          </div>

          {/* Team Plan */}
          <div className="bg-gray-800 p-8 rounded-2xl border border-gray-700 hover:border-blue-500 transition-all">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-2">Pro</h3>
              <div className="mb-4">
                <span className="text-5xl font-bold">$7.99</span>
                <span className="text-gray-400 text-lg">/month</span>
              </div>
              <p className="text-gray-300">For teams and organizations</p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Everything in Starter</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Unlimited premium GPT-4o summaries</span>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="w-4 h-4 text-gray-400 hover:text-gray-300 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[300px]">
                    <p>GPT-4o is our custom, premium model used with additional training data and prompts to produce higher quality summaries.</p>
                    <p>Up to 90 minute videos</p>
                  </TooltipContent>
                </Tooltip>
              </li>
              Viewer Reactions Summary
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Auto Chapter Summaries</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Topic-Based Summary View</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Quiz/Flashcards Mode</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Video Summary Analytics</span>
              </li>
              <li className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-400" />
                <span>Dedicated support</span>
              </li>
            </ul>

            <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all">
              Upgrade to Pro
            </button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="max-w-4xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Frequently Asked Questions</h2>
          <p className="text-gray-300 text-lg">Everything you need to know about Nobot pricing.</p>
        </div>

        <div className="space-y-8">
          <div className="bg-gray-800 p-6 rounded-2xl">
            <h3 className="text-xl font-bold mb-3">Can I change plans anytime?</h3>
            <p className="text-gray-300">Yes! You can upgrade, downgrade, or cancel your plan at any time. Changes take effect immediately, and we&apos;ll prorate any billing adjustments.</p>
          </div>

          <div className="bg-gray-800 p-6 rounded-2xl">
            <h3 className="text-xl font-bold mb-3">What happens to my summaries if I downgrade?</h3>
            <p className="text-gray-300">Your existing summaries will remain accessible, but you&apos;ll be limited to the features of your new plan. We never delete your data.</p>
          </div>

          <div className="bg-gray-800 p-6 rounded-2xl">
            <h3 className="text-xl font-bold mb-3">Is there a limit to video length?</h3>
            <p className="text-gray-300">Free users can summarize videos up to 2 hours. Pro and Team users have no length restrictions - summarize 8-hour lectures if you want!</p>
          </div>

          <div className="bg-gray-800 p-6 rounded-2xl">
            <h3 className="text-xl font-bold mb-3">Do you offer student discounts?</h3>
            <p className="text-gray-300">Yes! Students get 50% off Pro plans with a valid .edu email address. Contact us for details.</p>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 p-12 rounded-3xl border border-purple-500/30">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-8">
            <span className="text-2xl">🤖</span>
          </div>

          <h2 className="text-4xl font-bold mb-6">Ready to get started?</h2>
          <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
            Join thousands of users who are already saving hours every day with Nobot.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-4 rounded-lg font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all transform hover:scale-105">
              Start Free Trial
            </button>
            <button className="bg-transparent border-2 border-gray-600 px-8 py-4 rounded-lg font-bold text-lg hover:border-white transition-all">
              Contact Sales
            </button>
          </div>
        </div>
      </section>
      </div>
    </TooltipProvider>
  );
};

export default Pricing;