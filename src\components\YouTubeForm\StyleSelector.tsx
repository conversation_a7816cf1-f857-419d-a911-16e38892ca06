'use client';

import Select from '../ui/SelectPlain';
import { useFormContext } from './FormContext';
import { OutputStyle } from '@/app/actions';

export default function StyleSelector() {
  const { style, setStyle } = useFormContext();

  const styleOptions = [
    { value: 'insightful', label: 'Insightful' },
    { value: 'funny', label: 'Funny' },
    { value: 'actionable', label: 'Actionable' },
    { value: 'controversial', label: 'Controversial' },
    { value: 'educational', label: 'Educational' },
    { value: 'technical', label: 'Technical' }
  ];

  return (
    <Select
      id="style"
      value={style}
      onChange={(value) => setStyle(value as OutputStyle)}
      options={styleOptions}
      title="Choose the tone and style of your summary"
    />
  );
}
