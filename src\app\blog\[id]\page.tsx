import React from 'react';
import Link from 'next/link';
import { CalendarDays, Clock, ArrowRight } from 'lucide-react';
import { getBlogPostById, getRelatedPosts, getAllBlogPostIds, type BlogPost } from '@/lib/blog-data';
import { notFound } from 'next/navigation';

interface BlogPostPageProps {
  params: {
    id: string;
  };
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  const ids = getAllBlogPostIds();
  return ids.map((id) => ({
    id: id.toString(),
  }));
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: BlogPostPageProps) {
  const post = getBlogPostById(parseInt(params.id));
  
  if (!post) {
    return {
      title: 'Post not found',
    };
  }

  return {
    title: `${post.title} | Nobot Blog`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: [post.image],
    },
  };
}

const BlogPost = ({ params }: BlogPostPageProps) => {
  const post = getBlogPostById(parseInt(params.id));

  if (!post) {
    notFound();
  }

  const relatedPosts = getRelatedPosts(post.id);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Hero Image Section */}
      <div className="relative h-96 overflow-hidden">
        <img 
          src={post.image} 
          alt={post.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent"></div>
        
        {/* Article Header Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="max-w-4xl mx-auto">
            <Link 
              href="/blog" 
              className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-6 transition-colors"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Back to Blog
            </Link>
            
            <div className="flex items-center space-x-4 text-sm text-gray-300 mb-4">
              <span className="bg-purple-500/20 backdrop-blur-sm text-purple-300 px-3 py-1 rounded-full">
                {post.category}
              </span>
              <div className="flex items-center space-x-1">
                <CalendarDays className="w-4 h-4" />
                <span>{new Date(post.date).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{post.readTime}</span>
              </div>
            </div>
            
            <h1 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {post.title}
            </h1>
            
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl">
              {post.excerpt}
            </p>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <article 
          className="prose prose-lg prose-invert max-w-none text-gray-300 prose-headings:text-white prose-headings:font-bold prose-a:text-purple-400 prose-a:no-underline hover:prose-a:text-purple-300"
          dangerouslySetInnerHTML={{ __html: post.content }}
        />

        {/* Share and Navigation */}
        <div className="mt-16 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <Link 
              href="/blog" 
              className="inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              All Articles
            </Link>
            
            <div className="text-center">
              <p className="text-gray-400 text-sm mb-3">Ready to experience AI summarization?</p>
              <Link 
                href="/"
                className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-3 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all inline-block"
              >
                Get Started Free
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <div className="bg-gray-800/30 border-t border-gray-800">
          <div className="max-w-7xl mx-auto px-6 py-16">
            <h2 className="text-3xl font-bold mb-12 text-center">Related Articles</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {relatedPosts.map((relatedPost: BlogPost) => (
                <div key={relatedPost.id} className="group">
                  <div className="relative h-48 rounded-lg overflow-hidden mb-4">
                    <img 
                      src={relatedPost.image} 
                      alt={relatedPost.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"></div>
                    <div className="absolute bottom-3 left-3">
                      <span className="bg-purple-500/20 backdrop-blur-sm text-purple-300 px-2 py-1 rounded-full text-xs">
                        {relatedPost.category}
                      </span>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-purple-300 transition-colors">
                    <Link href={`/blog/${relatedPost.id}`}>
                      {relatedPost.title}
                    </Link>
                  </h3>
                  <p className="text-gray-400 text-sm line-clamp-2">
                    {relatedPost.excerpt}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogPost;
